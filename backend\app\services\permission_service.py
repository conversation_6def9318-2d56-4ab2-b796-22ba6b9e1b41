"""
权限控制服务层
实现权限检查、权限配置和权限矩阵管理的核心业务逻辑
"""
import time
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from app.core.database import get_db_session
from app.models.role import RoleDBModel, PermissionDBModel
from app.models.permission_control import (
    APIEndpointDBModel, RoleAPIPermissionDBModel, PermissionCheckLogDBModel
)
from app.models.api_route import APIRouteDBModel
from app.schemas.permission_control import (
    PermissionCheckRequest, RoleAPIPermissionRequest, BatchPermissionUpdateRequest,
    PermissionMatrixUpdateRequest
)


def format_datetime(dt: datetime) -> str:
    """格式化datetime为ISO 8601格式"""
    if dt is None:
        return ""
    return dt.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"


class PermissionService:
    """权限控制服务类"""
    
    def _generate_id(self, prefix: str) -> str:
        """生成唯一ID"""
        import random
        timestamp = int(time.time() * 1000)
        random_suffix = random.randint(1000, 9999)
        return f"{prefix}_{timestamp}_{random_suffix}"
    
    def _get_user_roles(self, user_id: str, db: Session) -> List[Dict[str, Any]]:
        """获取用户角色列表（模拟实现）"""
        # 这里应该从用户表获取用户角色，现在模拟实现
        # 实际项目中需要根据用户ID查询用户角色关联表
        
        # 模拟用户角色映射
        user_role_mapping = {
            "user_001": ["sales_manager"],
            "user_002": ["customer_service"],
            "user_003": ["super_admin"],
            "admin_user_001": ["super_admin"],
            "test_user": ["regular_user"],
            "integration_test_user": ["regular_user"],
            # 真实业务角色映射
            "front_desk_user_001": ["front_desk"],
            "coach_user_001": ["coach"],
            "front_desk_user_002": ["front_desk"],
            "coach_user_002": ["coach"],
            "manager_user_001": ["sales_manager"],
            "admin_user_002": ["super_admin"]
        }
        
        role_codes = user_role_mapping.get(user_id, ["regular_user"])
        
        # 查询角色详细信息
        roles = db.query(RoleDBModel).filter(RoleDBModel.code.in_(role_codes)).all()
        
        return [
            {
                "id": role.id,
                "name": role.name,
                "code": role.code,
                "level": role.level
            }
            for role in roles
        ]
    
    def _log_permission_check(self, user_id: str, resource: str, action: str, 
                            permission_required: str, is_allowed: bool, 
                            granted_by_role: str = None, denial_reason: str = None,
                            request_context: Dict = None, user_roles: List = None,
                            db: Session = None):
        """记录权限检查日志"""
        try:
            log_id = self._generate_id("perm_log")
            log = PermissionCheckLogDBModel(
                id=log_id,
                user_id=user_id,
                resource=resource,
                action=action,
                permission_required=permission_required,
                is_allowed=is_allowed,
                granted_by_role=granted_by_role,
                denial_reason=denial_reason,
                request_context=request_context or {},
                user_roles=[role.get("code") for role in user_roles] if user_roles else []
            )
            db.add(log)
            db.commit()
        except Exception as e:
            print(f"记录权限检查日志失败: {e}")
    
    # 权限检查方法
    def check_permission(self, request: PermissionCheckRequest, 
                        operator_id: str = None) -> Dict[str, Any]:
        """检查用户是否有访问特定API的权限"""
        try:
            with get_db_session() as db:
                # 获取用户角色
                user_roles = self._get_user_roles(request.user_id, db)
                
                if not user_roles:
                    # 记录日志
                    self._log_permission_check(
                        request.user_id, request.resource, request.action,
                        f"{request.resource}:{request.action}", False,
                        denial_reason="用户没有任何角色", db=db
                    )
                    
                    return {
                        "success": True,
                        "allowed": False,
                        "data": {
                            "allowed": False,
                            "user": {
                                "id": request.user_id,
                                "name": f"用户{request.user_id}",
                                "roles": []
                            },
                            "permission": {
                                "resource": request.resource,
                                "action": request.action,
                                "required_permission": f"{request.resource}:{request.action}"
                            },
                            "reason": "用户没有任何角色",
                            "suggestions": [
                                "联系管理员分配角色",
                                "检查用户账户状态"
                            ],
                            "checked_at": format_datetime(datetime.now())
                        }
                    }
                
                # 检查权限
                permission_required = f"{request.resource}:{request.action}"
                granted_by_role = None
                
                # 遍历用户角色，检查是否有所需权限
                for role in user_roles:
                    role_obj = db.query(RoleDBModel).filter(RoleDBModel.id == role["id"]).first()
                    if role_obj:
                        # 检查角色是否有所需权限
                        role_permissions = [p.name for p in role_obj.permissions]
                        has_permission = (
                            permission_required in role_permissions or
                            f"{request.resource}:*" in role_permissions or
                            "*:*" in role_permissions
                        )
                        
                        if has_permission:
                            granted_by_role = f"role:{role['code']}"
                            break
                
                # 构建响应
                if granted_by_role:
                    # 有权限
                    self._log_permission_check(
                        request.user_id, request.resource, request.action,
                        permission_required, True, granted_by_role,
                        request_context=request.context, user_roles=user_roles, db=db
                    )
                    
                    return {
                        "success": True,
                        "allowed": True,
                        "data": {
                            "allowed": True,
                            "user": {
                                "id": request.user_id,
                                "name": f"用户{request.user_id}",
                                "roles": [role["code"] for role in user_roles]
                            },
                            "permission": {
                                "resource": request.resource,
                                "action": request.action,
                                "granted_by": granted_by_role,
                                "permission_name": permission_required
                            },
                            "context": request.context or {},
                            "checked_at": format_datetime(datetime.now())
                        }
                    }
                else:
                    # 无权限
                    role_names = [role["name"] for role in user_roles]
                    denial_reason = f"用户角色 {', '.join(role_names)} 没有 '{permission_required}' 权限"
                    
                    self._log_permission_check(
                        request.user_id, request.resource, request.action,
                        permission_required, False, denial_reason=denial_reason,
                        request_context=request.context, user_roles=user_roles, db=db
                    )
                    
                    return {
                        "success": True,
                        "allowed": False,
                        "data": {
                            "allowed": False,
                            "user": {
                                "id": request.user_id,
                                "name": f"用户{request.user_id}",
                                "roles": [role["code"] for role in user_roles]
                            },
                            "permission": {
                                "resource": request.resource,
                                "action": request.action,
                                "required_permission": permission_required
                            },
                            "reason": denial_reason,
                            "suggestions": [
                                "联系管理员申请权限",
                                "使用具有相应权限的账户"
                            ],
                            "checked_at": format_datetime(datetime.now())
                        }
                    }
                
        except Exception as e:
            return {
                "success": False,
                "error": "permission_check_failed",
                "details": f"权限检查失败: {str(e)}"
            }
    
    def get_user_accessible_apis(self, user_id: str, resource: str = None, 
                               method: str = None, include_details: bool = False) -> Dict[str, Any]:
        """获取指定用户可以访问的API列表"""
        try:
            with get_db_session() as db:
                # 获取用户角色
                user_roles = self._get_user_roles(user_id, db)
                
                if not user_roles:
                    return {
                        "success": True,
                        "data": {
                            "user": {
                                "id": user_id,
                                "name": f"用户{user_id}",
                                "roles": []
                            },
                            "accessible_apis": [],
                            "summary": {
                                "total_apis": 0,
                                "by_resource": {},
                                "by_method": {},
                                "permissions": []
                            }
                        }
                    }
                
                # 获取用户所有权限
                user_permissions = set()
                role_details = []
                
                for role in user_roles:
                    role_obj = db.query(RoleDBModel).filter(RoleDBModel.id == role["id"]).first()
                    if role_obj:
                        role_details.append({
                            "id": role_obj.id,
                            "name": role_obj.name,
                            "code": role_obj.code,
                            "level": role_obj.level
                        })
                        
                        # 获取角色权限
                        permissions = list(role_obj.permissions)
                        for perm in permissions:
                            user_permissions.add(perm.name)
                
                # 获取所有API端点
                api_query = db.query(APIEndpointDBModel).filter(APIEndpointDBModel.is_active == True)
                
                # 应用筛选条件
                if resource:
                    api_query = api_query.filter(APIEndpointDBModel.resource == resource)
                if method:
                    api_query = api_query.filter(APIEndpointDBModel.method == method)
                
                all_apis = api_query.all()
                
                # 筛选用户可访问的API
                accessible_apis = []
                for api in all_apis:
                    # 检查用户是否有权限访问此API
                    required_permission = api.permission
                    has_access = False
                    granted_by = None
                    
                    # 检查精确匹配、资源通配符、全局通配符
                    for perm in user_permissions:
                        if (perm == required_permission or 
                            perm == f"{api.resource}:*" or 
                            perm == "*:*"):
                            has_access = True
                            # 找到授权角色
                            for role in role_details:
                                role_obj = db.query(RoleDBModel).filter(RoleDBModel.id == role["id"]).first()
                                if role_obj:
                                    role_perms = [p.name for p in role_obj.permissions]
                                    if perm in role_perms:
                                        granted_by = f"role:{role['code']}"
                                        break
                            break
                    
                    if has_access:
                        api_info = {
                            "api_id": api.id,
                            "name": api.name,
                            "endpoint": api.endpoint,
                            "method": api.method,
                            "resource": api.resource,
                            "action": api.action,
                            "permission": api.permission,
                            "granted_by": granted_by or "unknown"
                        }
                        accessible_apis.append(api_info)
                
                # 统计信息
                by_resource = {}
                by_method = {}
                permissions = list(user_permissions)
                
                for api in accessible_apis:
                    by_resource[api["resource"]] = by_resource.get(api["resource"], 0) + 1
                    by_method[api["method"]] = by_method.get(api["method"], 0) + 1
                
                return {
                    "success": True,
                    "data": {
                        "user": {
                            "id": user_id,
                            "name": f"用户{user_id}",
                            "roles": role_details
                        },
                        "accessible_apis": accessible_apis,
                        "summary": {
                            "total_apis": len(accessible_apis),
                            "by_resource": by_resource,
                            "by_method": by_method,
                            "permissions": permissions
                        }
                    }
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": "query_failed",
                "details": f"获取用户API权限失败: {str(e)}"
            }

    # 权限配置管理方法
    def update_role_api_permission(self, request: RoleAPIPermissionRequest,
                                 operator_id: str = None) -> Dict[str, Any]:
        """更新角色对特定API的访问权限"""
        try:
            with get_db_session() as db:
                # 查询角色
                role = db.query(RoleDBModel).filter(RoleDBModel.id == request.role_id).first()
                if not role:
                    return {
                        "success": False,
                        "error": "role_not_found",
                        "details": f"角色 {request.role_id} 不存在"
                    }

                # 查询API端点
                api = db.query(APIEndpointDBModel).filter(APIEndpointDBModel.id == request.api_id).first()
                if not api:
                    # 如果静态API不存在，尝试创建一个基于API ID的默认API端点
                    print(f"⚠️ API端点 {request.api_id} 不存在，尝试自动创建...")

                    # 根据API ID推断API信息
                    if request.api_id == "user_profile_api":
                        api_data = {
                            "id": "user_profile_api",
                            "name": "用户资料管理API",
                            "endpoint": "/api/users/profile",
                            "method": "GET",
                            "resource": "users",
                            "action": "profile",
                            "permission": "users:profile",
                            "description": "获取用户个人资料信息，支持用户ID查询和权限验证",
                            "is_public": False,
                            "is_active": True,
                            "min_role_level": 1
                        }
                    elif request.api_id == "doc_test_api":
                        api_data = {
                            "id": "doc_test_api",
                            "name": "文档测试API",
                            "endpoint": "/api/doc/test",
                            "method": "GET",
                            "resource": "doc",
                            "action": "test",
                            "permission": "doc:test",
                            "description": "文档符合性测试",
                            "is_public": False,
                            "is_active": True,
                            "min_role_level": 1
                        }
                    else:
                        return {
                            "success": False,
                            "error": "api_not_found",
                            "details": f"API {request.api_id} 不存在且无法自动创建"
                        }

                    # 创建API端点
                    api = APIEndpointDBModel(**api_data)
                    db.add(api)
                    db.flush()
                    print(f"✅ 自动创建API端点: {api.name}")

                # 查询权限 - 支持通用权限名称
                permission_name = request.permission
                if permission_name == "access":
                    # 如果是通用访问权限，使用API的具体权限
                    permission_name = api.permission

                permission = db.query(PermissionDBModel).filter(
                    PermissionDBModel.name == permission_name
                ).first()
                if not permission:
                    # 如果权限不存在，自动创建
                    permission_id = self._generate_id("perm")
                    permission = PermissionDBModel(
                        id=permission_id,
                        name=permission_name,
                        description=f"自动创建的权限: {permission_name}",
                        resource=api.resource,
                        action=api.action
                    )
                    db.add(permission)
                    db.flush()  # 确保权限被创建

                # 执行权限操作
                if request.action == "grant":
                    # 授予权限
                    if permission not in role.permissions:
                        role.permissions.append(permission)
                        role.permission_count = len(list(role.permissions))

                        print(f"✅ 为角色 {role.name} 授予权限 {permission.name}")
                        print(f"📊 角色权限数量更新为: {role.permission_count}")
                        print(f"🔍 角色当前所有权限: {[p.name for p in role.permissions]}")

                        # 记录权限分配日志
                        from app.models.role import RolePermissionLogDBModel
                        log_id = self._generate_id("log")
                        log = RolePermissionLogDBModel(
                            id=log_id,
                            role_id=request.role_id,
                            permission_id=permission.id,
                            operation="grant",
                            operator_id=operator_id,
                            reason=f"通过API {api.name} 授予权限"
                        )
                        db.add(log)

                        db.commit()
                        print(f"💾 权限更新已提交到数据库")

                        return {
                            "success": True,
                            "data": {
                                "role": {
                                    "id": role.id,
                                    "name": role.name,
                                    "code": role.code
                                },
                                "api": {
                                    "id": api.id,
                                    "name": api.name,
                                    "endpoint": api.endpoint,
                                    "method": api.method
                                },
                                "permission": {
                                    "name": request.permission,
                                    "action": "grant",
                                    "granted_at": format_datetime(datetime.now()),
                                    "granted_by": operator_id or "system"
                                }
                            }
                        }
                    else:
                        return {
                            "success": False,
                            "error": "permission_already_granted",
                            "details": f"角色 {role.name} 已经拥有权限 {request.permission}"
                        }

                elif request.action == "revoke":
                    # 撤销权限
                    if permission in role.permissions:
                        role.permissions.remove(permission)
                        role.permission_count = len(list(role.permissions))

                        # 记录权限撤销日志
                        from app.models.role import RolePermissionLogDBModel
                        log_id = self._generate_id("log")
                        log = RolePermissionLogDBModel(
                            id=log_id,
                            role_id=request.role_id,
                            permission_id=permission.id,
                            operation="revoke",
                            operator_id=operator_id,
                            reason=f"通过API {api.name} 撤销权限"
                        )
                        db.add(log)

                        db.commit()

                        return {
                            "success": True,
                            "data": {
                                "role": {
                                    "id": role.id,
                                    "name": role.name,
                                    "code": role.code
                                },
                                "api": {
                                    "id": api.id,
                                    "name": api.name,
                                    "endpoint": api.endpoint,
                                    "method": api.method
                                },
                                "permission": {
                                    "name": request.permission,
                                    "action": "revoke",
                                    "revoked_at": format_datetime(datetime.now()),
                                    "revoked_by": operator_id or "system"
                                }
                            }
                        }
                    else:
                        return {
                            "success": False,
                            "error": "permission_not_granted",
                            "details": f"角色 {role.name} 没有权限 {request.permission}"
                        }

        except Exception as e:
            return {
                "success": False,
                "error": "update_failed",
                "details": f"更新角色API权限失败: {str(e)}"
            }

    def batch_update_permissions(self, request: BatchPermissionUpdateRequest,
                               operator_id: str = None) -> Dict[str, Any]:
        """批量更新多个角色的API权限"""
        try:
            results = []
            successful = 0
            failed = 0
            granted = 0
            revoked = 0

            for update in request.updates:
                if request.dry_run:
                    # 试运行模式，只验证不执行
                    result = {
                        "role_id": update.role_id,
                        "api_id": update.api_id,
                        "permission": update.permission,
                        "action": update.action,
                        "status": "dry_run",
                        "message": "试运行模式，未实际执行"
                    }
                    results.append(result)
                    successful += 1
                else:
                    # 实际执行更新
                    update_result = self.update_role_api_permission(update, operator_id)

                    if update_result["success"]:
                        result = {
                            "role_id": update.role_id,
                            "api_id": update.api_id,
                            "permission": update.permission,
                            "action": update.action,
                            "status": "success",
                            "message": "权限更新成功"
                        }
                        successful += 1
                        if update.action == "grant":
                            granted += 1
                        else:
                            revoked += 1
                    else:
                        result = {
                            "role_id": update.role_id,
                            "api_id": update.api_id,
                            "permission": update.permission,
                            "action": update.action,
                            "status": "failed",
                            "message": update_result.get("details", "更新失败"),
                            "error": update_result.get("error", "unknown_error")
                        }
                        failed += 1

                    results.append(result)

            return {
                "success": True,
                "data": {
                    "results": results,
                    "summary": {
                        "total": len(request.updates),
                        "successful": successful,
                        "failed": failed,
                        "granted": granted,
                        "revoked": revoked
                    }
                }
            }

        except Exception as e:
            return {
                "success": False,
                "error": "batch_update_failed",
                "details": f"批量权限更新失败: {str(e)}"
            }

    # 权限矩阵管理方法
    def get_permission_matrix(self, format_type: str = "matrix",
                            include_inactive: bool = False) -> Dict[str, Any]:
        """获取完整的权限矩阵"""
        try:
            with get_db_session() as db:
                # 获取角色列表
                role_query = db.query(RoleDBModel)
                if not include_inactive:
                    role_query = role_query.filter(RoleDBModel.status == "active")

                roles = role_query.all()

                # 获取API列表 - 优先使用静态配置的API端点
                apis = db.query(APIEndpointDBModel).filter(
                    APIEndpointDBModel.is_active == True
                ).all()

                # 如果没有静态API，创建一些默认的测试API
                if not apis:
                    print("⚠️ 未找到静态API端点，创建默认测试API...")
                    # 这里可以添加创建默认API的逻辑

                # 构建角色信息
                role_list = []
                for role in roles:
                    role_list.append({
                        "id": role.id,
                        "name": role.name,
                        "code": role.code,
                        "level": role.level
                    })

                # 构建API信息
                api_list = []
                for api in apis:
                    api_list.append({
                        "id": api.id,
                        "name": api.name,
                        "endpoint": api.endpoint,
                        "method": api.method,
                        "permission": api.permission
                    })

                # 构建权限矩阵
                permissions_matrix = {}
                total_permissions = 0
                granted_permissions = 0

                for role in roles:
                    role_permissions = {}
                    # 重新查询角色权限以确保数据是最新的
                    fresh_role = db.query(RoleDBModel).filter(RoleDBModel.id == role.id).first()
                    role_perms = [p.name for p in fresh_role.permissions] if fresh_role else []

                    print(f"🔍 角色 {role.name} 的权限: {role_perms}")

                    for api in apis:
                        total_permissions += 1
                        # 检查角色是否有此API的权限
                        has_permission = (
                            api.permission in role_perms or
                            f"{api.resource}:*" in role_perms or
                            "*:*" in role_perms
                        )
                        role_permissions[api.id] = has_permission
                        if has_permission:
                            granted_permissions += 1
                            print(f"✅ 角色 {role.name} 有权限访问 {api.name}")
                        else:
                            print(f"❌ 角色 {role.name} 无权限访问 {api.name}")

                    permissions_matrix[role.id] = role_permissions

                # 计算统计信息
                denied_permissions = total_permissions - granted_permissions
                coverage = (granted_permissions / total_permissions * 100) if total_permissions > 0 else 0

                return {
                    "success": True,
                    "data": {
                        "matrix": {
                            "roles": role_list,
                            "apis": api_list,
                            "permissions": permissions_matrix
                        },
                        "statistics": {
                            "total_roles": len(roles),
                            "total_apis": len(apis),
                            "total_permissions": total_permissions,
                            "granted_permissions": granted_permissions,
                            "denied_permissions": denied_permissions,
                            "coverage": round(coverage, 1)
                        }
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "matrix_query_failed",
                "details": f"获取权限矩阵失败: {str(e)}"
            }

    def update_permission_matrix(self, request: PermissionMatrixUpdateRequest,
                               operator_id: str = None) -> Dict[str, Any]:
        """更新权限矩阵"""
        try:
            with get_db_session() as db:
                updated_permissions = {}
                roles_updated = 0
                permissions_granted = 0
                permissions_revoked = 0

                for role_id, api_permissions in request.matrix.items():
                    # 查询角色
                    role = db.query(RoleDBModel).filter(RoleDBModel.id == role_id).first()
                    if not role:
                        continue

                    role_changes = {"granted": [], "revoked": []}

                    for api_id, should_have_permission in api_permissions.items():
                        # 查询API端点
                        api = db.query(APIEndpointDBModel).filter(APIEndpointDBModel.id == api_id).first()
                        if not api:
                            continue

                        permission_name = api.permission

                        # 查询权限
                        permission = db.query(PermissionDBModel).filter(
                            PermissionDBModel.name == permission_name
                        ).first()
                        if not permission:
                            # 自动创建权限
                            permission_id = self._generate_id("perm")
                            permission = PermissionDBModel(
                                id=permission_id,
                                name=permission_name,
                                description=f"自动创建的权限: {permission_name}",
                                resource=api.resource,
                                action=api.action
                            )
                            db.add(permission)
                            db.flush()

                        # 检查当前权限状态
                        has_permission = permission in role.permissions

                        if should_have_permission and not has_permission:
                            # 需要授予权限
                            role.permissions.append(permission)
                            role_changes["granted"].append(api_id)
                            permissions_granted += 1

                        elif not should_have_permission and has_permission:
                            # 需要撤销权限
                            role.permissions.remove(permission)
                            role_changes["revoked"].append(api_id)
                            permissions_revoked += 1

                    if role_changes["granted"] or role_changes["revoked"]:
                        # 更新权限计数
                        role.permission_count = len(list(role.permissions))
                        updated_permissions[role_id] = role_changes
                        roles_updated += 1

                db.commit()

                return {
                    "success": True,
                    "data": {
                        "updated_permissions": updated_permissions,
                        "summary": {
                            "roles_updated": roles_updated,
                            "permissions_granted": permissions_granted,
                            "permissions_revoked": permissions_revoked,
                            "total_changes": permissions_granted + permissions_revoked
                        },
                        "updated_at": format_datetime(datetime.now())
                    }
                }

        except Exception as e:
            return {
                "success": False,
                "error": "matrix_update_failed",
                "details": f"更新权限矩阵失败: {str(e)}"
            }
