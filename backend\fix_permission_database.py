#!/usr/bin/env python3
"""
权限数据库修复脚本
直接在数据库中创建所需的API端点和权限数据
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def fix_permission_database():
    """修复权限数据库"""
    try:
        from app.core.database import get_db_session
        from app.models.permission_control import APIEndpointDBModel
        from app.models.role import PermissionDBModel, RoleDBModel
        import time
        import random
        
        def generate_id(prefix):
            timestamp = int(time.time() * 1000)
            random_suffix = random.randint(1000, 9999)
            return f"{prefix}_{timestamp}_{random_suffix}"
        
        print("🔧 开始修复权限数据库...")
        
        with get_db_session() as db:
            # 1. 检查并创建前端需要的API端点
            print("1️⃣ 检查API端点...")
            
            required_apis = [
                {
                    "id": "user_profile_api",
                    "name": "用户资料管理API",
                    "endpoint": "/api/users/profile",
                    "method": "GET",
                    "resource": "users",
                    "action": "profile",
                    "permission": "users:profile",
                    "description": "获取用户个人资料信息，支持用户ID查询和权限验证",
                    "is_public": False,
                    "is_active": True,
                    "min_role_level": 1
                },
                {
                    "id": "doc_test_api",
                    "name": "文档测试API",
                    "endpoint": "/api/doc/test",
                    "method": "GET",
                    "resource": "doc",
                    "action": "test",
                    "permission": "doc:test",
                    "description": "文档符合性测试",
                    "is_public": False,
                    "is_active": True,
                    "min_role_level": 1
                }
            ]
            
            created_apis = 0
            for api_data in required_apis:
                existing = db.query(APIEndpointDBModel).filter(
                    APIEndpointDBModel.id == api_data["id"]
                ).first()
                
                if not existing:
                    api_endpoint = APIEndpointDBModel(**api_data)
                    db.add(api_endpoint)
                    created_apis += 1
                    print(f"   ✅ 创建API端点: {api_data['name']}")
                else:
                    print(f"   ✓ API端点已存在: {api_data['name']}")
            
            # 2. 检查并创建对应的权限
            print("2️⃣ 检查权限...")
            
            required_permissions = [
                {
                    "name": "users:profile",
                    "description": "用户资料管理权限",
                    "resource": "users",
                    "action": "profile"
                },
                {
                    "name": "doc:test",
                    "description": "文档测试权限",
                    "resource": "doc",
                    "action": "test"
                }
            ]
            
            created_permissions = 0
            for perm_data in required_permissions:
                existing = db.query(PermissionDBModel).filter(
                    PermissionDBModel.name == perm_data["name"]
                ).first()
                
                if not existing:
                    permission = PermissionDBModel(
                        id=generate_id("perm"),
                        **perm_data
                    )
                    db.add(permission)
                    created_permissions += 1
                    print(f"   ✅ 创建权限: {perm_data['name']}")
                else:
                    print(f"   ✓ 权限已存在: {perm_data['name']}")
            
            # 3. 检查产品经理角色
            print("3️⃣ 检查产品经理角色...")
            
            product_manager = db.query(RoleDBModel).filter(
                RoleDBModel.code == "product_manager"
            ).first()
            
            if product_manager:
                print(f"   ✓ 找到产品经理角色: {product_manager.name} (ID: {product_manager.id})")
                
                # 检查角色权限
                current_permissions = [p.name for p in product_manager.permissions]
                print(f"   当前权限: {current_permissions}")
                
                # 为产品经理添加文档测试权限（如果没有的话）
                doc_test_permission = db.query(PermissionDBModel).filter(
                    PermissionDBModel.name == "doc:test"
                ).first()
                
                if doc_test_permission and doc_test_permission not in product_manager.permissions:
                    product_manager.permissions.append(doc_test_permission)
                    product_manager.permission_count = len(list(product_manager.permissions))
                    print(f"   ✅ 为产品经理添加文档测试权限")
                else:
                    print(f"   ✓ 产品经理已有文档测试权限")
            else:
                print("   ❌ 未找到产品经理角色")
            
            # 提交所有更改
            db.commit()
            
            print(f"\n📊 修复结果:")
            print(f"   创建API端点: {created_apis} 个")
            print(f"   创建权限: {created_permissions} 个")
            
            # 4. 验证修复结果
            print("\n4️⃣ 验证修复结果...")
            
            total_apis = db.query(APIEndpointDBModel).count()
            total_permissions = db.query(PermissionDBModel).count()
            total_roles = db.query(RoleDBModel).count()
            
            print(f"   总API端点数: {total_apis}")
            print(f"   总权限数: {total_permissions}")
            print(f"   总角色数: {total_roles}")
            
            # 显示前端需要的API
            print("\n📋 前端权限管理可用的API:")
            apis = db.query(APIEndpointDBModel).filter(
                APIEndpointDBModel.is_active == True
            ).all()
            
            for api in apis:
                print(f"   • {api.name} ({api.method} {api.endpoint}) - {api.permission}")
            
            print("\n✅ 权限数据库修复完成！")
            return True
            
    except Exception as e:
        print(f"❌ 权限数据库修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_permission_api():
    """测试权限API"""
    try:
        import requests
        
        print("\n🔧 测试权限API...")
        
        headers = {
            "Authorization": "Bearer dev_token_12345",
            "Content-Type": "application/json"
        }
        
        # 测试获取权限矩阵
        print("1️⃣ 测试获取权限矩阵...")
        response = requests.get("http://localhost:5000/api/permissions/matrix", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            matrix = data.get("data", {}).get("matrix", {})
            roles = matrix.get("roles", [])
            apis = matrix.get("apis", [])
            
            print(f"   ✅ 权限矩阵获取成功")
            print(f"   角色数: {len(roles)}")
            print(f"   API数: {len(apis)}")
            
            # 查找产品经理和文档测试API
            product_manager_id = None
            doc_test_api_id = None
            
            for role in roles:
                if role.get("code") == "product_manager":
                    product_manager_id = role.get("id")
                    break
            
            for api in apis:
                if api.get("name") == "文档测试API":
                    doc_test_api_id = api.get("id")
                    break
            
            if product_manager_id and doc_test_api_id:
                print(f"   ✅ 找到产品经理角色: {product_manager_id}")
                print(f"   ✅ 找到文档测试API: {doc_test_api_id}")
                return True
            else:
                print(f"   ❌ 未找到所需的角色或API")
                return False
        else:
            print(f"   ❌ 权限矩阵获取失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 权限API测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始权限系统修复...")
    
    # 1. 修复数据库
    if fix_permission_database():
        print("\n🎉 权限数据库修复成功！")
        
        # 2. 测试API（如果服务正在运行）
        try:
            if test_permission_api():
                print("\n🎉 权限API测试通过！")
            else:
                print("\n⚠️ 权限API测试失败，但数据库修复成功")
        except:
            print("\n⚠️ 无法连接到API服务，但数据库修复成功")
    else:
        print("\n❌ 权限数据库修复失败！")

if __name__ == "__main__":
    main()
