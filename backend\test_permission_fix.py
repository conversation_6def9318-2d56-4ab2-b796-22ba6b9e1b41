#!/usr/bin/env python3
"""
权限控制系统修复测试脚本
测试权限保存和加载功能
"""
import requests
import json
import time

# 配置
BASE_URL = "http://localhost:5000"
DEV_TOKEN = "dev_token_12345"

def get_headers():
    """获取请求头"""
    return {
        "Authorization": f"Bearer {DEV_TOKEN}",
        "Content-Type": "application/json"
    }

def test_get_roles():
    """测试获取角色列表"""
    print("🔍 测试获取角色列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/roles", headers=get_headers())
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            roles = data.get("data", {}).get("roles", [])
            print(f"✅ 获取到 {len(roles)} 个角色")
            
            # 找到产品经理角色
            product_manager = None
            for role in roles:
                if role.get("code") == "product_manager":
                    product_manager = role
                    break
            
            if product_manager:
                print(f"✅ 找到产品经理角色: {product_manager['name']} (ID: {product_manager['id']})")
                print(f"   权限数量: {product_manager.get('permission_count', 0)}")
                return product_manager
            else:
                print("❌ 未找到产品经理角色")
                return None
        else:
            print(f"❌ 获取角色列表失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_get_routes():
    """测试获取API路由列表"""
    print("\n🔍 测试获取API路由列表...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/routes", headers=get_headers())
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            routes = data.get("data", {}).get("routes", [])
            print(f"✅ 获取到 {len(routes)} 个API路由")
            
            for route in routes[:3]:  # 显示前3个
                print(f"   • {route.get('name')} ({route.get('method')} {route.get('endpoint')})")
            
            return routes
        else:
            print(f"❌ 获取API路由列表失败: {response.text}")
            return []
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return []

def test_get_permission_matrix():
    """测试获取权限矩阵"""
    print("\n🔍 测试获取权限矩阵...")
    
    try:
        response = requests.get(f"{BASE_URL}/api/permissions/matrix", headers=get_headers())
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            matrix_data = data.get("data", {})
            matrix = matrix_data.get("matrix", {})
            
            roles = matrix.get("roles", [])
            apis = matrix.get("apis", [])
            permissions = matrix.get("permissions", {})
            
            print(f"✅ 权限矩阵获取成功")
            print(f"   角色数量: {len(roles)}")
            print(f"   API数量: {len(apis)}")
            print(f"   权限矩阵大小: {len(permissions)}")
            
            # 查找产品经理角色和文档测试API
            product_manager_id = None
            doc_test_api_id = None
            
            for role in roles:
                if role.get("code") == "product_manager":
                    product_manager_id = role.get("id")
                    print(f"   找到产品经理角色ID: {product_manager_id}")
                    break
            
            for api in apis:
                if api.get("name") == "文档测试API":
                    doc_test_api_id = api.get("id")
                    print(f"   找到文档测试API ID: {doc_test_api_id}")
                    break
            
            # 检查当前权限状态
            if product_manager_id and doc_test_api_id:
                current_permission = permissions.get(product_manager_id, {}).get(doc_test_api_id, False)
                print(f"   当前权限状态: {current_permission}")
                return {
                    "matrix": matrix,
                    "product_manager_id": product_manager_id,
                    "doc_test_api_id": doc_test_api_id,
                    "current_permission": current_permission
                }
            else:
                print("❌ 未找到所需的角色或API")
                return None
        else:
            print(f"❌ 获取权限矩阵失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None

def test_batch_update_permissions(product_manager_id, doc_test_api_id, grant_permission=True):
    """测试批量更新权限"""
    action = "grant" if grant_permission else "revoke"
    print(f"\n🔧 测试批量更新权限 ({action})...")
    
    # 构建请求数据
    request_data = {
        "updates": [
            {
                "role_id": product_manager_id,
                "api_id": doc_test_api_id,
                "permission": "doc:test",
                "action": action
            }
        ],
        "dry_run": False
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/permissions/batch-update",
            headers=get_headers(),
            json=request_data
        )
        print(f"状态码: {response.status_code}")
        print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 批量权限更新成功")
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            results = data.get("data", {}).get("results", [])
            summary = data.get("data", {}).get("summary", {})
            
            print(f"   更新结果: {len(results)} 个操作")
            print(f"   成功: {summary.get('successful', 0)}")
            print(f"   失败: {summary.get('failed', 0)}")
            
            return True
        else:
            print(f"❌ 批量权限更新失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始权限控制系统修复测试...")
    
    # 1. 测试获取角色列表
    product_manager = test_get_roles()
    if not product_manager:
        print("❌ 无法获取产品经理角色，测试终止")
        return
    
    # 2. 测试获取API路由列表
    routes = test_get_routes()
    
    # 3. 测试获取权限矩阵
    matrix_info = test_get_permission_matrix()
    if not matrix_info:
        print("❌ 无法获取权限矩阵，测试终止")
        return
    
    product_manager_id = matrix_info["product_manager_id"]
    doc_test_api_id = matrix_info["doc_test_api_id"]
    current_permission = matrix_info["current_permission"]
    
    # 4. 测试权限更新
    if current_permission:
        print("\n📝 当前已有权限，测试撤销权限...")
        success = test_batch_update_permissions(product_manager_id, doc_test_api_id, False)
    else:
        print("\n📝 当前无权限，测试授予权限...")
        success = test_batch_update_permissions(product_manager_id, doc_test_api_id, True)
    
    if success:
        # 5. 等待一下，然后重新检查权限矩阵
        print("\n⏳ 等待2秒后重新检查权限矩阵...")
        time.sleep(2)
        
        new_matrix_info = test_get_permission_matrix()
        if new_matrix_info:
            new_permission = new_matrix_info["current_permission"]
            print(f"\n📊 权限更新结果:")
            print(f"   更新前: {current_permission}")
            print(f"   更新后: {new_permission}")
            
            if new_permission != current_permission:
                print("✅ 权限更新成功！")
            else:
                print("❌ 权限更新失败，状态未改变")
        else:
            print("❌ 无法重新获取权限矩阵")
    
    print("\n🎉 权限控制系统测试完成！")

if __name__ == "__main__":
    main()
