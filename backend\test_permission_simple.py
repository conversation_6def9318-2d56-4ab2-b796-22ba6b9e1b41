#!/usr/bin/env python3
"""
简单的权限控制系统测试
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_permission_service():
    """测试权限服务"""
    try:
        from app.services.permission_service import PermissionService
        from app.schemas.permission_control import BatchPermissionUpdateRequest, RoleAPIPermissionRequest
        
        print("🔧 测试权限服务...")
        
        # 创建权限服务实例
        permission_service = PermissionService()
        
        # 测试获取权限矩阵
        print("1️⃣ 测试获取权限矩阵...")
        matrix_result = permission_service.get_permission_matrix()
        
        if matrix_result["success"]:
            matrix_data = matrix_result["data"]
            roles = matrix_data["matrix"]["roles"]
            apis = matrix_data["matrix"]["apis"]
            permissions = matrix_data["matrix"]["permissions"]
            
            print(f"✅ 权限矩阵获取成功")
            print(f"   角色数量: {len(roles)}")
            print(f"   API数量: {len(apis)}")
            print(f"   权限矩阵大小: {len(permissions)}")
            
            # 查找产品经理角色
            product_manager_id = None
            for role in roles:
                if role.get("code") == "product_manager":
                    product_manager_id = role.get("id")
                    print(f"   找到产品经理角色: {role['name']} (ID: {product_manager_id})")
                    break
            
            # 查找API
            test_api_id = None
            if apis:
                test_api_id = apis[0]["id"]
                print(f"   使用测试API: {apis[0]['name']} (ID: {test_api_id})")
            
            if product_manager_id and test_api_id:
                # 测试批量权限更新
                print("\n2️⃣ 测试批量权限更新...")
                
                # 创建更新请求
                update_request = RoleAPIPermissionRequest(
                    role_id=product_manager_id,
                    api_id=test_api_id,
                    permission="access",
                    action="grant"
                )
                
                batch_request = BatchPermissionUpdateRequest(
                    updates=[update_request],
                    dry_run=False
                )
                
                # 执行更新
                update_result = permission_service.batch_update_permissions(batch_request)
                
                if update_result["success"]:
                    print("✅ 批量权限更新成功")
                    results = update_result["data"]["results"]
                    summary = update_result["data"]["summary"]
                    
                    print(f"   更新结果: {len(results)} 个操作")
                    print(f"   成功: {summary.get('successful', 0)}")
                    print(f"   失败: {summary.get('failed', 0)}")
                    
                    # 重新获取权限矩阵验证
                    print("\n3️⃣ 验证权限更新结果...")
                    new_matrix_result = permission_service.get_permission_matrix()
                    
                    if new_matrix_result["success"]:
                        new_permissions = new_matrix_result["data"]["matrix"]["permissions"]
                        current_permission = new_permissions.get(product_manager_id, {}).get(test_api_id, False)
                        print(f"   更新后权限状态: {current_permission}")
                        
                        if current_permission:
                            print("✅ 权限更新验证成功！")
                            return True
                        else:
                            print("❌ 权限更新验证失败")
                            return False
                    else:
                        print(f"❌ 重新获取权限矩阵失败: {new_matrix_result.get('error')}")
                        return False
                else:
                    print(f"❌ 批量权限更新失败: {update_result.get('error')}")
                    print(f"   详情: {update_result.get('details')}")
                    return False
            else:
                print("❌ 未找到所需的角色或API")
                return False
        else:
            print(f"❌ 权限矩阵获取失败: {matrix_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """测试数据库连接"""
    try:
        from app.core.database import get_db_session
        
        print("🔧 测试数据库连接...")
        
        with get_db_session() as db:
            # 测试查询
            from app.models.role import RoleDBModel
            roles = db.query(RoleDBModel).limit(5).all()
            print(f"✅ 数据库连接成功，查询到 {len(roles)} 个角色")
            
            for role in roles:
                print(f"   • {role.name} (级别: {role.level})")
            
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始权限控制系统简单测试...")
    
    # 1. 测试数据库连接
    if not test_database_connection():
        print("❌ 数据库连接测试失败，终止测试")
        return
    
    # 2. 测试权限服务
    if test_permission_service():
        print("\n🎉 权限控制系统测试通过！")
    else:
        print("\n❌ 权限控制系统测试失败！")

if __name__ == "__main__":
    main()
