/**
 * 开发者配置主组件 - Apple风格设计
 */

import React, { useState, useEffect } from 'react';
import { Button, Space } from 'antd';
import {
  ArrowLeftOutlined,
  HomeOutlined,
  UserOutlined,
  SettingOutlined,
  AppstoreOutlined,
  DatabaseOutlined,
  ApiOutlined,
  TeamOutlined,
  CodeOutlined,
  CheckCircleOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import AuthenticationStep from './steps/AuthenticationStep';
import ScenarioConfigStep from './steps/ScenarioConfigStep';
import TemplateSelectionStep from './steps/TemplateSelectionStep';
import EntityModelingStep from './steps/EntityModelingStep';
import APIRouteStep from './steps/APIRouteStep';
import WorkflowDesignStep from './steps/WorkflowDesignStep';
import RoleManagementStep from './steps/RoleManagementStep';
import { useDeveloperAuth } from '../../hooks/developer/useDeveloperAuth';
import type { TemplateListItem } from '../../types/developer/template';
import './DeveloperConfigMain.css';

interface Step {
  id: number;
  title: string;
  description: string;
  icon: React.ReactNode;
  completed: boolean;
  active: boolean;
  enabled: boolean;
}

const DeveloperConfigMain: React.FC = () => {
  const navigate = useNavigate();
  const { authState, logout } = useDeveloperAuth();
  const [currentStep, setCurrentStep] = useState(1);
  const [completedSteps, setCompletedSteps] = useState<number[]>([]);
  const [stepRefs, setStepRefs] = useState<{ [key: number]: any }>({});
  const [selectedTemplate, setSelectedTemplate] = useState<TemplateListItem | null>(null);

  // 步骤配置
  const steps: Step[] = [
    {
      id: 1,
      title: '开发者认证',
      description: '验证开发者身份',
      icon: <UserOutlined />,
      completed: completedSteps.includes(1),
      active: currentStep === 1,
      enabled: true
    },
    {
      id: 2,
      title: '场景配置',
      description: '配置业务场景',
      icon: <SettingOutlined />,
      completed: completedSteps.includes(2),
      active: currentStep === 2,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 3,
      title: '模板选择',
      description: '选择项目模板',
      icon: <AppstoreOutlined />,
      completed: completedSteps.includes(3),
      active: currentStep === 3,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 4,
      title: '实体建模',
      description: '设计数据模型',
      icon: <DatabaseOutlined />,
      completed: completedSteps.includes(4),
      active: currentStep === 4,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 5,
      title: 'API路由管理',
      description: '配置接口路由',
      icon: <ApiOutlined />,
      completed: completedSteps.includes(5),
      active: currentStep === 5,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 6,
      title: '工作流设计',
      description: '设计业务流程',
      icon: <BranchesOutlined />,
      completed: completedSteps.includes(6),
      active: currentStep === 6,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 7,
      title: '角色管理',
      description: '配置用户角色和权限',
      icon: <TeamOutlined />,
      completed: completedSteps.includes(7),
      active: currentStep === 7,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 8,
      title: '权限控制',
      description: '配置权限控制规则',
      icon: <SettingOutlined />,
      completed: completedSteps.includes(8),
      active: currentStep === 8,
      enabled: true // 临时允许所有步骤访问
    },
    {
      id: 9,
      title: '代码生成部署',
      description: '生成并部署代码',
      icon: <CodeOutlined />,
      completed: completedSteps.includes(9),
      active: currentStep === 9,
      enabled: true // 临时允许所有步骤访问
    }
  ];

  // 检查认证状态
  useEffect(() => {
    if (authState.isAuthenticated && !completedSteps.includes(1)) {
      setCompletedSteps([1]);
      setCurrentStep(2);
    }
  }, [authState.isAuthenticated, completedSteps]);

  const handleBackToHome = () => {
    navigate('/');
  };

  const handleLogout = () => {
    logout();
    setCurrentStep(1);
    setCompletedSteps([]);
  };

  const handleAuthSuccess = () => {
    setCompletedSteps([1]);
    setCurrentStep(2);
  };

  const handleScenarioSuccess = () => {
    setCompletedSteps(prev => [...prev, 2]);
    setCurrentStep(3);
  };

  const handleTemplateSelect = (template: TemplateListItem) => {
    setSelectedTemplate(template);
  };

  const handleTemplateSuccess = () => {
    setCompletedSteps(prev => [...prev, 3]);
    setCurrentStep(4);
  };

  const handleEntityModelingSuccess = () => {
    setCompletedSteps(prev => [...prev, 4]);
    setCurrentStep(5);
  };

  const handleAPIRouteSuccess = () => {
    setCompletedSteps(prev => [...prev, 5]);
    setCurrentStep(6);
  };

  const handleWorkflowDesignSuccess = () => {
    setCompletedSteps(prev => [...prev, 6]);
    setCurrentStep(7);
  };

  const handleRoleManagementSuccess = () => {
    setCompletedSteps(prev => [...prev, 7]);
    setCurrentStep(8);
  };

  const handleStepClick = (stepId: number) => {
    const step = steps.find(s => s.id === stepId);
    if (step && step.enabled) {
      setCurrentStep(stepId);
    }
  };

  const handleStepBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSave = () => {
    // 触发当前步骤的保存操作
    const currentStepRef = stepRefs[currentStep];
    if (currentStepRef && currentStepRef.handleSave) {
      currentStepRef.handleSave();
    }
  };

  const getStepTitle = () => {
    const step = steps.find(s => s.id === currentStep);
    return step ? step.title : '配置步骤';
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <AuthenticationStep onSuccess={handleAuthSuccess} />;
      case 2:
        return (
          <ScenarioConfigStep
            onSuccess={handleScenarioSuccess}
            onBack={handleStepBack}
          />
        );
      case 3:
        return (
          <TemplateSelectionStep
            onTemplateSelect={handleTemplateSelect}
            onNext={handleTemplateSuccess}
          />
        );
      case 4:
        return (
          <EntityModelingStep
            onNext={handleEntityModelingSuccess}
            onPrev={handleStepBack}
            scenarioId="current_scenario" // TODO: 从场景配置中获取
          />
        );
      case 5:
        return (
          <APIRouteStep
            onNext={handleAPIRouteSuccess}
            onPrev={handleStepBack}
          />
        );
      case 6:
        return (
          <WorkflowDesignStep
            onNext={handleWorkflowDesignSuccess}
            onPrev={handleStepBack}
          />
        );
      case 7:
        return (
          <RoleManagementStep
            onNext={handleRoleManagementSuccess}
            onPrev={handleStepBack}
          />
        );
      default:
        return (
          <div className="step-placeholder">
            <div className="placeholder-content">
              <div className="placeholder-icon">
                {steps.find(s => s.id === currentStep)?.icon}
              </div>
              <h3 className="placeholder-title">
                {steps.find(s => s.id === currentStep)?.title}
              </h3>
              <p className="placeholder-description">
                该步骤正在开发中，敬请期待
              </p>
              <div className="placeholder-features">
                <div className="feature-item">
                  <CheckCircleOutlined />
                  <span>功能设计已完成</span>
                </div>
                <div className="feature-item">
                  <CheckCircleOutlined />
                  <span>界面原型已确定</span>
                </div>
                <div className="feature-item">
                  <CheckCircleOutlined />
                  <span>即将开始开发</span>
                </div>
              </div>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="developer-config-main">
      {/* Apple风格顶部导航栏 */}
      <div className="config-header">
        <div className="header-content">
          <div className="header-left">
            <h1 className="main-title">AILF</h1>
            <span className="subtitle">开发者配置面板</span>
          </div>
          <div className="header-right">
            <Space size="middle">
              {authState.isAuthenticated && (
                <Button
                  type="text"
                  onClick={handleLogout}
                  className="logout-button"
                >
                  退出登录
                </Button>
              )}
              <Button
                type="primary"
                icon={<HomeOutlined />}
                onClick={handleBackToHome}
                className="home-button"
              >
                返回主页
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 主要内容区域 - Apple风格左右布局 */}
      <div className="config-main">
        {/* 左侧步骤导航 */}
        <div className="steps-sidebar">
          <div className="steps-container">
            <div className="steps-header">
              <h2 className="steps-title">配置步骤</h2>
              <p className="steps-subtitle">完成以下步骤来配置您的AILF项目</p>
            </div>

            <div className="steps-list">
              {steps.map((step, index) => (
                <div
                  key={step.id}
                  className={`step-item ${step.active ? 'active' : ''} ${step.completed ? 'completed' : ''} ${
                    step.enabled ? 'enabled' : 'disabled'
                  }`}
                  onClick={() => handleStepClick(step.id)}
                >
                  <div className="step-indicator">
                    <div className="step-number">
                      {step.completed ? <CheckCircleOutlined /> : step.id}
                    </div>
                    {index < steps.length - 1 && <div className="step-connector" />}
                  </div>
                  <div className="step-content">
                    <div className="step-icon">{step.icon}</div>
                    <div className="step-info">
                      <h3 className="step-title">{step.title}</h3>
                      <p className="step-description">{step.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="steps-footer">
              <div className="progress-info">
                <span className="progress-text">
                  已完成 {completedSteps.length} / {steps.length} 步骤
                </span>
                <div className="progress-bar">
                  <div
                    className="progress-fill"
                    style={{ width: `${(completedSteps.length / steps.length) * 100}%` }}
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 右侧内容区域 */}
        <div className="content-area">
          <div className="content-container">
            {renderStepContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default DeveloperConfigMain;
