/**
 * 角色管理步骤组件
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  message,
  Popconfirm,
  Tooltip,
  Badge,
  Divider,
  Row,
  Col,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UserOutlined,
  SafetyOutlined,
  SettingOutlined,
  TeamOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { RoleAPI, Role, CreateRoleRequest, UpdateRoleRequest } from '../../../services/developer/roleAPI';
import './RoleManagementStep.css';

const { Option } = Select;
const { TextArea } = Input;

interface RoleManagementStepProps {
  onNext?: () => void;
  onPrev?: () => void;
}

const RoleManagementStep: React.FC<RoleManagementStepProps> = ({ onNext, onPrev }) => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [form] = Form.useForm();
  const [summary, setSummary] = useState({
    total_roles: 0,
    active_roles: 0,
    inactive_roles: 0,
    total_users: 0,
    avg_permissions_per_role: 0
  });

  // 角色级别配置
  const roleLevels = [
    { value: 1, label: '访客用户', color: '#87d068' },
    { value: 2, label: '普通用户', color: '#108ee9' },
    { value: 3, label: '业务用户', color: '#2db7f5' },
    { value: 4, label: '高级用户', color: '#87d068' },
    { value: 5, label: '主管用户', color: '#f50' },
    { value: 6, label: '经理用户', color: '#fa8c16' },
    { value: 7, label: '部门经理', color: '#fa541c' },
    { value: 8, label: '系统管理员', color: '#722ed1' },
    { value: 9, label: '超级管理员', color: '#eb2f96' },
    { value: 10, label: '系统所有者', color: '#f5222d' }
  ];

  // 获取角色级别标签
  const getLevelTag = (level: number) => {
    const levelConfig = roleLevels.find(l => l.value === level);
    return levelConfig ? (
      <Tag color={levelConfig.color}>{levelConfig.label}</Tag>
    ) : (
      <Tag>{`级别 ${level}`}</Tag>
    );
  };

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusConfig = {
      active: { color: 'success', text: '活跃' },
      inactive: { color: 'default', text: '非活跃' },
      deprecated: { color: 'warning', text: '已弃用' },
      locked: { color: 'error', text: '已锁定' }
    };
    const config = statusConfig[status as keyof typeof statusConfig] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 加载角色列表
  const loadRoles = async () => {
    setLoading(true);
    try {
      const response = await RoleAPI.getRoles();
      setRoles(response.roles);
      setSummary(response.summary);
    } catch (error) {
      message.error('加载角色列表失败');
      console.error('Load roles error:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadRoles();
  }, []);

  // 打开创建/编辑模态框
  const openModal = (role?: Role) => {
    setEditingRole(role || null);
    setModalVisible(true);
    if (role) {
      form.setFieldsValue({
        name: role.name,
        code: role.code,
        level: role.level,
        description: role.description,
        status: role.status
      });
    } else {
      form.resetFields();
    }
  };

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false);
    setEditingRole(null);
    form.resetFields();
  };

  // 保存角色
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingRole) {
        // 更新角色
        const updateData: UpdateRoleRequest = {
          name: values.name,
          description: values.description,
          level: values.level,
          status: values.status
        };
        await RoleAPI.updateRole(editingRole.id, updateData);
        message.success('角色更新成功');
      } else {
        // 创建角色
        const createData: CreateRoleRequest = {
          name: values.name,
          code: values.code,
          level: values.level,
          description: values.description,
          status: values.status || 'active'
        };
        await RoleAPI.createRole(createData);
        message.success('角色创建成功');
      }
      
      closeModal();
      loadRoles();
    } catch (error) {
      message.error(editingRole ? '角色更新失败' : '角色创建失败');
      console.error('Save role error:', error);
    }
  };

  // 删除角色
  const handleDelete = async (roleId: string) => {
    try {
      await RoleAPI.deleteRole(roleId);
      message.success('角色删除成功');
      loadRoles();
    } catch (error) {
      message.error('角色删除失败');
      console.error('Delete role error:', error);
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Role) => (
        <div>
          <div style={{ fontWeight: 500 }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>代码: {record.code}</div>
        </div>
      )
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      render: (level: number) => getLevelTag(level),
      sorter: (a: Role, b: Role) => a.level - b.level
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '用户数量',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (count: number) => (
        <Badge count={count} showZero color="#52c41a" />
      )
    },
    {
      title: '权限数量',
      dataIndex: 'permission_count',
      key: 'permission_count',
      render: (count: number) => (
        <Badge count={count || 0} showZero color="#1890ff" />
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <span>{text || '-'}</span>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Role) => (
        <Space size="small">
          <Tooltip title="编辑角色">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Tooltip title="权限管理">
            <Button
              type="text"
              icon={<SafetyOutlined />}
              onClick={() => {/* TODO: 打开权限管理 */}}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个角色吗？"
            description="删除后将无法恢复，关联用户将被重新分配。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Tooltip title="删除角色">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="role-management-step">
      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总角色数"
              value={summary.total_roles}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃角色"
              value={summary.active_roles}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={summary.total_users}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均权限数"
              value={summary.avg_permissions_per_role}
              precision={1}
              prefix={<SafetyOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 角色列表 */}
      <Card
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            <TeamOutlined />
            <span>角色管理</span>
          </div>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => openModal()}
          >
            创建角色
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个角色`
          }}
        />
      </Card>

      {/* 创建/编辑角色模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '创建角色'}
        open={modalVisible}
        onOk={handleSave}
        onCancel={closeModal}
        width={600}
        okText="保存"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{ status: 'active', level: 1 }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="角色名称"
                name="name"
                rules={[{ required: true, message: '请输入角色名称' }]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="角色代码"
                name="code"
                rules={[
                  { required: true, message: '请输入角色代码' },
                  { pattern: /^[a-z_]+$/, message: '只能包含小写字母和下划线' }
                ]}
              >
                <Input 
                  placeholder="请输入角色代码" 
                  disabled={!!editingRole}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="角色级别"
                name="level"
                rules={[{ required: true, message: '请选择角色级别' }]}
              >
                <Select placeholder="请选择角色级别">
                  {roleLevels.map(level => (
                    <Option key={level.value} value={level.value}>
                      {level.label} (级别 {level.value})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="角色状态"
                name="status"
                rules={[{ required: true, message: '请选择角色状态' }]}
              >
                <Select placeholder="请选择角色状态">
                  <Option value="active">活跃</Option>
                  <Option value="inactive">非活跃</Option>
                  <Option value="deprecated">已弃用</Option>
                  <Option value="locked">已锁定</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            label="角色描述"
            name="description"
          >
            <TextArea
              rows={3}
              placeholder="请输入角色描述"
              maxLength={200}
              showCount
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 底部操作按钮 */}
      <div className="step-actions">
        <Space>
          {onPrev && (
            <Button onClick={onPrev}>
              上一步
            </Button>
          )}
          {onNext && (
            <Button type="primary" onClick={onNext}>
              下一步
            </Button>
          )}
        </Space>
      </div>
    </div>
  );
};

export default RoleManagementStep;
